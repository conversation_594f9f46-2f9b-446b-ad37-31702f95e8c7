#include "sys.h"
/***********************************************
公司：轮趣科技（东莞）有限公司
品牌：WHEELTEC
官网：wheeltec.net
淘宝店铺：shop114407458.taobao.com 
速卖通: https://minibalance.aliexpress.com/store/4455017
版本：V1.0
修改时间：2024-11-14

Brand: WHEELTEC
Website: wheeltec.net
Taobao shop: shop114407458.taobao.com 
Aliexpress: https://minibalance.aliexpress.com/store/4455017
Version: V1.0
Update：2024-11-14
All rights reserved
***********************************************/



int main(void)
{
	NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2); //设置中断分组为2
	delay_init(72);                 //=====延时初始化

	uart_init(115200);							//串口初始化
	ATD5984_Init();									//ATD5984初始化，实际是初始化控制ATD5984的SLEEP和DIR两个引脚
	STEP12_PWM_Init(7199, 6);			//定时器8初始化，给ATD5984的STEP引脚提供PWM信号
	
	TIM2_Init(199, 7199);				//10ms进一次中断处理函数
//	STEP12_PWM_Init(65535, 14);
	while(1)
	{     		

	}
}
