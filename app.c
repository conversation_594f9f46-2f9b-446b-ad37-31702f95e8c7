#include "app.h"

/***********************************************
步进电机控制测试和使用示例
功能：验证步进电机API的正确性和稳定性
***********************************************/

// 基本功能测试
void StepperMotor_BasicTest(void)
{
    printf("=== 步进电机基本功能测试 ===\r\n");
    
    // 设置电机速度
    StepperMotor_SetSpeed(MOTOR_1, 200);  // 电机1设置为200RPM
    StepperMotor_SetSpeed(MOTOR_2, 150);  // 电机2设置为150RPM
    
    printf("电机速度设置完成\r\n");
    
    // 测试电机1转动90度
    printf("电机1开始转动到90度...\r\n");
    MotorError_t error = StepperMotor_SetAngle(MOTOR_1, 90.0f);
    if(error == MOTOR_OK) {
        printf("电机1运动指令发送成功\r\n");
    } else {
        printf("电机1运动指令失败，错误代码: %d\r\n", error);
    }
    
    // 等待电机1运动完成
    while(StepperMotor_IsMoving(MOTOR_1)) {
        delay_ms(100);
        float current_angle = StepperMotor_GetCurrentAngle(MOTOR_1);
        printf("电机1当前角度: %.1f度\r\n", current_angle);
    }
    
    printf("电机1运动完成，最终角度: %.1f度\r\n", StepperMotor_GetCurrentAngle(MOTOR_1));
}

// 双路电机独立控制测试
void StepperMotor_DualTest(void)
{
    printf("\r\n=== 双路电机独立控制测试 ===\r\n");
    
    // 同时启动两个电机
    MotorError_t error = StepperMotor_SetBothAngles(180.0f, -90.0f);
    if(error == MOTOR_OK) {
        printf("双电机运动指令发送成功\r\n");
        printf("电机1目标: 180度, 电机2目标: -90度\r\n");
    } else {
        printf("双电机运动指令失败，错误代码: %d\r\n", error);
    }
    
    // 监控两个电机的运动状态
    while(StepperMotor_IsMoving(MOTOR_1) || StepperMotor_IsMoving(MOTOR_2)) {
        delay_ms(200);
        
        float angle1 = StepperMotor_GetCurrentAngle(MOTOR_1);
        float angle2 = StepperMotor_GetCurrentAngle(MOTOR_2);
        MotorState_t state1 = StepperMotor_GetState(MOTOR_1);
        MotorState_t state2 = StepperMotor_GetState(MOTOR_2);
        
        printf("电机1: %.1f度 (状态:%d), 电机2: %.1f度 (状态:%d)\r\n", 
               angle1, state1, angle2, state2);
    }
    
    printf("双电机运动完成\r\n");
    printf("电机1最终角度: %.1f度\r\n", StepperMotor_GetCurrentAngle(MOTOR_1));
    printf("电机2最终角度: %.1f度\r\n", StepperMotor_GetCurrentAngle(MOTOR_2));
}// 相对运动测试
void StepperMotor_RelativeTest(void)
{
    printf("\r\n=== 相对运动测试 ===\r\n");
    
    // 回零操作
    StepperMotor_Home(MOTOR_1);
    StepperMotor_Home(MOTOR_2);
    printf("电机回零完成\r\n");
    
    // 相对运动测试
    printf("电机1相对运动 +45度...\r\n");
    StepperMotor_MoveRelative(MOTOR_1, 45.0f);
    StepperMotor_WaitForStop(MOTOR_1);
    printf("电机1当前角度: %.1f度\r\n", StepperMotor_GetCurrentAngle(MOTOR_1));
    
    printf("电机1相对运动 -30度...\r\n");
    StepperMotor_MoveRelative(MOTOR_1, -30.0f);
    StepperMotor_WaitForStop(MOTOR_1);
    printf("电机1当前角度: %.1f度\r\n", StepperMotor_GetCurrentAngle(MOTOR_1));
}

// 错误处理测试
void StepperMotor_ErrorTest(void)
{
    printf("\r\n=== 错误处理测试 ===\r\n");
    
    // 测试无效电机ID
    MotorError_t error = StepperMotor_SetAngle(5, 90.0f);
    printf("无效电机ID测试，错误代码: %d\r\n", error);
    
    // 测试超出范围的角度
    error = StepperMotor_SetAngle(MOTOR_1, 5000.0f);
    printf("超出范围角度测试，错误代码: %d\r\n", error);
    
    // 测试无效速度
    error = StepperMotor_SetSpeed(MOTOR_1, 2000);
    printf("无效速度测试，错误代码: %d\r\n", error);
    
    // 清除错误状态
    StepperMotor_ClearError(MOTOR_1);
    printf("错误状态已清除\r\n");
}

// 性能测试
void StepperMotor_PerformanceTest(void)
{
    printf("\r\n=== 性能测试 ===\r\n");
    
    uint32_t start_time, end_time;
    
    // 测试API响应时间
    start_time = delay_ms(0);  // 获取当前时间（简化）
    StepperMotor_SetAngle(MOTOR_1, 360.0f);
    end_time = delay_ms(0);
    printf("API响应时间: %d ms\r\n", end_time - start_time);
    
    // 测试高速运动
    StepperMotor_SetSpeed(MOTOR_1, 800);  // 高速800RPM
    printf("高速运动测试 (800RPM)...\r\n");
    StepperMotor_WaitForStop(MOTOR_1);
    printf("高速运动完成\r\n");
}

// 完整测试套件
void StepperMotor_FullTest(void)
{
    printf("\r\n========== 步进电机完整测试开始 ==========\r\n");
    
    // 执行所有测试
    StepperMotor_BasicTest();
    delay_ms(1000);
    
    StepperMotor_DualTest();
    delay_ms(1000);
    
    StepperMotor_RelativeTest();
    delay_ms(1000);
    
    StepperMotor_ErrorTest();
    delay_ms(1000);
    
    StepperMotor_PerformanceTest();
    
    printf("\r\n========== 步进电机完整测试结束 ==========\r\n");
}