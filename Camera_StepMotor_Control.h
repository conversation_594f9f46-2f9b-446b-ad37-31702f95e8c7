/**
 * 2025年电赛E题专用步进电机控制系统头文件
 * 
 * 应用场景：摄像头识别 -> 串口角度指令 -> 电机精确转动
 * 
 * 主要功能：
 * 1. 非阻塞电机控制，响应摄像头指令
 * 2. 串口命令解析和执行
 * 3. 精确角度控制和位置跟踪
 * 4. 实时状态反馈
 * 
 * 串口指令格式：
 * - "M1:90.5"     -> 电机1转动90.5度
 * - "M2:-45.0"    -> 电机2反转45度  
 * - "MB:180.0"    -> 双电机同步转动180度
 * - "POS"         -> 查询当前位置
 * - "STOP"        -> 紧急停止
 * - "RESET"       -> 位置归零
 * 
 * 版本：V1.0 电赛专用版
 */

#ifndef __CAMERA_STEPMOTOR_CONTROL_H
#define __CAMERA_STEPMOTOR_CONTROL_H

#include "sys.h"
#include "stm32f10x.h"

// ==================== 错误码定义 ====================
#define CAMERA_MOTOR_OK         0       // 成功
#define CAMERA_MOTOR_BUSY       1       // 系统忙
#define CAMERA_MOTOR_ERROR      2       // 错误

// ==================== 电机ID定义 ====================
#define MOTOR_1                 1       // 电机1
#define MOTOR_2                 2       // 电机2  
#define MOTOR_BOTH              3       // 双电机

// ==================== 核心API函数 ====================

/**
 * 系统初始化
 */
void Camera_StepMotor_Init(void);

/**
 * 主处理函数
 */
void Camera_StepMotor_Process(void);

/**
 * 串口数据处理
 */
void Camera_StepMotor_UART_Handler(uint8_t data);

/**
 * 检查系统状态
 */
uint8_t Camera_StepMotor_IsIdle(void);

/**
 * 获取当前位置
 */
void Camera_StepMotor_GetPosition(float* motor1_pos, float* motor2_pos);

/**
 * 直接角度控制
 */
uint8_t Camera_StepMotor_RotateAngle(uint8_t motor_id, float angle, uint16_t speed_rpm);

// ==================== 系统时间函数 ====================

/**
 * 更新系统时间
 */
void update_system_time(uint32_t interval_ms);

/**
 * 获取系统时间
 */
uint32_t get_system_time_ms(void);

/**
 * 重置系统时间
 */
void reset_system_time(void);

// ==================== 便捷宏定义 ====================

// 常用角度控制宏
#define CAMERA_ROTATE_M1(angle)     Camera_StepMotor_RotateAngle(MOTOR_1, angle, 30)
#define CAMERA_ROTATE_M2(angle)     Camera_StepMotor_RotateAngle(MOTOR_2, angle, 30)
#define CAMERA_ROTATE_BOTH(angle)   Camera_StepMotor_RotateAngle(MOTOR_BOTH, angle, 30)

// 状态检查宏
#define CAMERA_MOTOR_READY()        Camera_StepMotor_IsIdle()

#endif /* __CAMERA_STEPMOTOR_CONTROL_H */
